<div class="flex space-x-2 w-full pt-2">
  <mat-form-field appearance="outline" class="inputbox">
    <mat-label>Address Type</mat-label>
    <mat-select [(ngModel)]="addressData.ADDRESSTYPE" (ngModelChange)="onAddressFieldChange()">
      <mat-option *ngFor="let data of addressTypes" [value]="data.IDENTIFIER">{{ data.DESCRIPTION }}</mat-option>
    </mat-select>
  </mat-form-field>
  <mat-form-field appearance="outline" class="inputbox">
    <mat-label for="label">Address1</mat-label>
    <input matInput type="text" [(ngModel)]="addressData.ADDRESS1" (ngModelChange)="onAddressFieldChange()"
      aria-describedby="emailHelp" maxlength="100" autocomplete="off" />
  </mat-form-field>
  <mat-form-field appearance="outline" class="inputbox">
    <mat-label for="label">Address2</mat-label>
    <input matInput type="text" [(ngModel)]="addressData.ADDRESS2" (ngModelChange)="onAddressFieldChange()"
      aria-describedby="emailHelp" maxlength="100" autocomplete="off" />
  </mat-form-field>
  <mat-form-field appearance="outline" class="inputbox">
    <mat-label for="label">Address3</mat-label>
    <input matInput type="text" [(ngModel)]="addressData.ADDRESS3" (ngModelChange)="onAddressFieldChange()"
      aria-describedby="emailHelp" maxlength="100" autocomplete="off" />
  </mat-form-field>
</div>

<div class="flex space-x-2 w-full">
  <mat-form-field appearance="outline" class="inputbox">
    <mat-label for="label">Address4</mat-label>
    <input matInput type="text" [(ngModel)]="addressData.ADDRESS4" (ngModelChange)="onAddressFieldChange()"
      aria-describedby="emailHelp" maxlength="100" autocomplete="off" />
  </mat-form-field>
  <mat-form-field appearance="outline" class="inputbox">
    <mat-label>COUNTRY</mat-label>
    <mat-select [(ngModel)]="addressData.COUNTRY" (ngModelChange)="onAddressFieldChange()">
      <mat-option *ngFor="let data of countries" [value]="data.IDENTIFIER">{{ data.DESCRIPTION }}</mat-option>
    </mat-select>
  </mat-form-field>
  <mat-form-field appearance="outline" class="inputbox">
    <mat-label for="label">State Code</mat-label>
    <input matInput type="text" [(ngModel)]="addressData.STATE" (ngModelChange)="onAddressFieldChange()"
      aria-describedby="emailHelp" maxlength="100" autocomplete="off" />
  </mat-form-field>
  <mat-form-field appearance="outline" class="inputbox">
    <mat-label for="label">City</mat-label>
    <input matInput type="text" [(ngModel)]="addressData.CITY" (ngModelChange)="onAddressFieldChange()"
      aria-describedby="emailHelp" maxlength="100" autocomplete="off" />
  </mat-form-field>
  <mat-form-field appearance="outline" class="inputbox">
    <mat-label for="label">Postal Code</mat-label>
    <input matInput type="text" [(ngModel)]="addressData.POSTALCODE" (ngModelChange)="onAddressFieldChange()"
      aria-describedby="emailHelp" maxlength="100" autocomplete="off" />
  </mat-form-field>
</div>

<div class="flex space-x-2">


  <!-- Address Phone Numbers -->
  <form [formGroup]="phoneForm" class="flex gap-x-2" *ngIf="phoneForm">
    <div class="h-max">
      <ngx-material-intl-tel-input [fieldControl]="phoneForm.get('phoneNumber3')" [autoIpLookup]="false"
        [appearance]="'outline'" [mainLabel]="'Address Phone 1'">
      </ngx-material-intl-tel-input>
      <div *ngIf="phoneForm.get('phoneNumber3')?.touched && phoneForm.get('phoneNumber3')?.invalid"
        class="error-message text-red-500 text-xs mt-1">
        Please enter a valid number
      </div>
    </div>

    <div class="h-max">
      <ngx-material-intl-tel-input [fieldControl]="phoneForm.get('phoneNumber4')" [autoIpLookup]="false"
        [appearance]="'outline'" [mainLabel]="'Address Phone 2'">
      </ngx-material-intl-tel-input>
      @if (phoneForm.get('phoneNumber4')?.touched && phoneForm.get('phoneNumber4')?.invalid){
      <div class="error-message text-red-500 text-xs mt-1">
        Please enter a valid number
      </div>
      }
    </div>
  </form>
</div>
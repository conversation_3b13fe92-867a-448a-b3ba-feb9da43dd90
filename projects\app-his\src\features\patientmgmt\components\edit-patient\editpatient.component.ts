import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { PatientService } from 'ecmed-api/visitmgmt';
import { CommonService,PatientFormComponent,PatientIndividualFormComponent } from 'his-components';
import { Location } from '@angular/common'
import { FillHeightDirective } from 'ec-ngcore/ui';
import { ApplicationService} from 'ec-ngcore'
@Component({
    standalone: true,
    imports: [PatientFormComponent, MatIconModule, MatButtonModule, FillHeightDirective],
    templateUrl: './editpatient.component.html',
    styleUrl: './editpatient.component.scss',
    encapsulation: ViewEncapsulation.None
})
export class EditpatientComponent implements OnInit {
    @ViewChild(PatientFormComponent) patientFormComponent?: PatientFormComponent;
    patient: any
    selectedPatient: any = null;
    patients: any[] = [];
    visitData: any;
    searchResults: any;
    savedindividualresult: any
    inputData: any;
    patientdata: any;
    patientresult: any;
    dialogdata: any;
    isLoading: boolean = false;
    newrouterid: any;

    constructor(
        public location: Location,
        public _dialog: MatDialog,
        public commonServices: CommonService,
        public _API: PatientService,
        private route: ActivatedRoute,
        public router: Router,
        private _currentRoute: ActivatedRoute,
        private _appService:ApplicationService) {
        this.simulateLoading();
    }

    private simulateLoading(): void {
        this.isLoading = true;
    }

    ngOnInit(): void {
        this.route.params.subscribe(params => {
            const id = params['id'];
            this.patientView(id);
        });
        const codeTypes = ['PT', 'SX', 'RE', 'RC', 'BG', 'NA', 'MS', 'TC', 'ED', 'ON', 'ID', 'LG', 'RT', 'AD', 'CY'];
        this.commonServices.fetchVisitData(codeTypes).subscribe(
            (response: any) => {
                this.visitData = response;
            }
        );
    }

    savePatient() {
        this.patientFormComponent?.handleSaveUpdatePatient('update')
    }

    handleedit(event: any) {
        // Parse the JSON string and wrap it in the correct API structure
        const patientData = JSON.parse(event);
        const requestParams = {
            ecmedWebapiModelsPatientmgmtEditModel: patientData
        };

        this._API.patientUpdatePost(requestParams).subscribe((result: any) => {
            if (result) {
                let url = window.location.href
                if (url.includes("his-psd/service/patients/edit/")) {
                    this.router.navigate(['view' + result], { relativeTo: this._currentRoute });
                } else {
                    this.router.navigate(['his-psd/patients/view/' + result]);
                }
            }
        });
    }

    onDateChanged(dateOfBirth: any): void {
        const dateOfBirthString: string = dateOfBirth.toISOString();
        this.commonServices.calculateAge(dateOfBirthString);
    }

    handleSearchData(searchData: any) {
        if (!searchData || !searchData.data) {
            return;
        }
        const { searchType, name, idNo, idType } = searchData.data;
        this._API.patientSearchIndividualGet(searchType, name, idNo, idType).subscribe(
            (results) => {
                this.searchResults = results;
                if (this.searchResults.length === 0) {
                    this.openConfirmDialog();
                } else {
                    this.searchResults = results;
                }
            }
        );
    }

    handleEventData(eventData: any): void {
        if (eventData.action === 'search') {
            this.handleSearchData(eventData);
        } else if (eventData.action === 'edit') {
            this.handleEditData(eventData);
        }
    }

    handleEditData(searchData: any) {
        if (!searchData || !searchData.data) {
            return;
        }
        const { searchType, name, idNo, idType } = searchData.data;
        const identifier = searchData.data.identifier;
        if (identifier) {
            this.openDialog(identifier);
        } else {

            this._API.patientSearchIndividualGet({searchType:searchType, 
                         name:name, iDType:idType, iDNo:idNo }).subscribe(
                (results: any) => {
                    if (results && results.length > 0) {
                        const identifierFromResults = (results[0] as any).IDENTIFIER;
                        if (identifierFromResults) {
                            this.openDialog(identifierFromResults);
                        }
                    }
                }
            );
        }
    }

    openConfirmDialog(): void {
        this._appService.confirmDialog({
            message: 'No results found for your search. Do you want to create a new detail?',
        }).afterClosed().subscribe((result: any) => {
            if (result == 'Yes') {
                this.openDialog();
            }
        });
    }

    openDialog(identifier?:string) {
        const dialogRef = this._dialog.open(PatientIndividualFormComponent, {
            width: "2500px",
            height: "850px",
            panelClass: "custom-dialog-container",
            data: identifier
        });
        dialogRef.afterClosed().subscribe(result => {
            if (result) {
                this.fetchPatientDetails(result);
            }
        });
    }

    fetchPatientDetails(id: string): void {
        this._API.patientGetIndividualDetailGet({id:id}).subscribe(
            (data: object | object[]) => {
                const patientDetails = Array.isArray(data) ? (data[0] as object) : (data as object);
                if (patientDetails) {
                    this.savedindividualresult = patientDetails;
                }
            }
        );
    }

    patientView(id:any) {
        this._API.patientGetDetailGet({id:id}).subscribe(
            (result) => {
                this.patient = result;
            }
        );
    }
    goback() {
        // let fullUrl = (window.location.href);
        // if (fullUrl.includes("/service/patients/edit")) {
        //   this.router.navigate(['/service/visitsdata/' + this.newrouterid]);
        // } else {
        //   this.router.navigate(['/patients']);
        // }
        this.location.back()
    }
}


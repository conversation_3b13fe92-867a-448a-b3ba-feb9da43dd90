import { CommonModule } from '@angular/common';
import { Component, ViewEncapsulation } from '@angular/core';
import { PatientIndividualFormComponent } from '../public-api';
import { EcmedWebapiModelsPatientmgmtEditIndividualModel, PatientSearchIndividualGetRequestParams, PatientService } from 'ecmed-api/visitmgmt';
import { CommonService} from '../common.service';
import { VisitData } from '../visit.model';
import { AutoFormatDateDirective } from '@his-components/services';
import { MatDividerModule } from '@angular/material/divider';
import { FormsModule, FormGroup, FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatFormField, MatLabel } from '@angular/material/form-field';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatOption } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatTabsModule } from '@angular/material/tabs';
import { MaterialPackage} from '@his-components/utils';
import { HisCommonAddressComponent } from '../../common/his-common-address/his-common-address.component';

@Component({
  selector: 'his-indv-new',
  standalone: true,
  imports: [CommonModule, MaterialPackage, AutoFormatDateDirective, HisCommonAddressComponent, ReactiveFormsModule],
  templateUrl: './new-individual.component.html',
  styleUrl: './new-individual.component.scss',
  encapsulation: ViewEncapsulation.None
})
export class NewIndividualComponent {
  constructor(public patientApi: PatientService, public commonServices: CommonService) { }

  patientDetails: EcmedWebapiModelsPatientmgmtEditIndividualModel = {};
  idType: any = "";
  idNo: any = "";
  hrn: any = "";
  title: any = "";
  name: any = "";
  gender: any = "";
  dob: any = "";
  age: any = "";
  ethnicity: any = "";
  place: any = "";
  nation: any = "";
  mail: any = "";
  religion: any = "";
  language: any = "";
  education: any = "";
  occupation: any = "";
  marital: any = "";
  addressType: any = "";
  lineA: any = "";
  lineB: any = "";
  lineC: any = "";
  lineD: any = "";
  postalCode: any = "";
  city: any = "";
  stateCode: any = "";
  country: any = "";
  //
  phoneA: any = "";
  phoneB: any = "";
  phoneC: any = "";
  phoneD: any = "";

  visitData?: VisitData;

  // Form group for phone numbers (required by his-common-address)
  myForm = new FormGroup({
    phoneNumber3: new FormControl(''),
    phoneNumber4: new FormControl('')
  });

  public handleOnChange(type:string, e:any) {
    console.log(type, e)
    switch (type) {
      case 'idType':
        this.idType = e;
        break;
      case 'idNo':
        this.idNo = e.target.value
        break;
      case 'hrn':
        this.hrn = e;
        break;
      case 'title':
        this.title = e
        break;
      case 'name':
        this.name = e.target.value;
        break;
      case 'gender':
        this.gender = e
        break;
      case 'dob':
        this.dob = new Date(e.value);
        console.log(this.dob)
        //
        let today = new Date();
        let birthDate = new Date(this.dob);
        let ages = today.getFullYear() - birthDate.getFullYear();
        let m = today.getMonth() - birthDate.getMonth();
        if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
          ages--;
        }
        ///
        console.log(ages)
        this.age = ages
        break;
      case 'ethnicity':
        this.ethnicity = e;
        break;
      case 'place':
        this.place = e
        break;
      case 'nation':
        this.nation = e;
        break;
      case 'religion':
        this.religion = e
        break;
      case 'mail':
        this.mail = e.target.value
        break;
      case 'language':
        this.language = e
        break;
      case 'education':
        this.education = e;
        break;
      case 'occupation':
        this.occupation = e
        break;
      case 'marital':
        this.marital = e;
        break;
      case 'addressType':
        this.addressType = e
        break;
      case 'lineA':
        this.lineA = e.target.value
        break;
      case 'lineB':
        this.lineB = e.target.value
        break;
      case 'lineC':
        this.lineC = e.target.value
        break;
      case 'lineD':
        this.lineD = e.target.value
        break;
      case 'postalCode':
        this.postalCode = e.target.value
        break;
      case 'stateCode':
        this.stateCode = e.target.value;
        break;
      case 'city':
        this.city = e.target.value
        break;
      case 'country':
        this.country = e;
        break;
    }
  }

  public handleSubmit() {
    let objSave1 = {
      ...this.patientDetails,
      Identifier: '',
      _OrgCode: 0,
      Merged: "N",
      PatientId: "0",
      HRN: "",
      Telephone: this.patientDetails.Telephone|| '' ,
      Telephone2: this.patientDetails.Telephone2  || '',
      Telephone3: this.myForm.get('phoneNumber3')?.value || '',
      Telephone4: this.myForm.get('phoneNumber4')?.value || '',
    }
    console.log(objSave1, 'save');
    const requestParams = {
      ecmedWebapiModelsPatientmgmtEditIndividualModel: objSave1
    };
    this.patientApi.patientCreateNewIndividualPost(requestParams).subscribe((res: any) => {
      // if (apiResult) {
      //   this.dialogRef.close(apiResult);
      // }
      console.log(res)
    });
  }

  public handleReset() {
    this.patientDetails = {};
    // Reset phone form as well
    this.myForm.reset();
  }

  // Method to get address data for his-common-address component
  getAddressData() {
    return {
      ADDRESSTYPE: this.patientDetails.AddressType || '',
      ADDRESS1: this.patientDetails.Address1 || '',
      ADDRESS2: this.patientDetails.Address2 || '',
      ADDRESS3: this.patientDetails.Address3 || '',
      ADDRESS4: this.patientDetails.Address4 || '',
      POSTALCODE: this.patientDetails.PostalCode || '',
      CITY: this.patientDetails.City || '',
      STATE: this.patientDetails.State || '',
      COUNTRY: this.patientDetails.Country || ''
    };
  }

  // Method to handle address changes from his-common-address component
  onAddressChange(addressData: any) {
    this.patientDetails.AddressType = addressData.ADDRESSTYPE;
    this.patientDetails.Address1 = addressData.ADDRESS1;
    this.patientDetails.Address2 = addressData.ADDRESS2;
    this.patientDetails.Address3 = addressData.ADDRESS3;
    this.patientDetails.Address4 = addressData.ADDRESS4;
    this.patientDetails.PostalCode = addressData.POSTALCODE;
    this.patientDetails.City = addressData.CITY;
    this.patientDetails.State = addressData.STATE;
    this.patientDetails.Country = addressData.COUNTRY;
  }

  public handleSearch(key:any) {
    {
      this.patientApi.patientSearchIndividualGet(
        <PatientSearchIndividualGetRequestParams>{searchType:"All",
           iDNo:this.patientDetails.IDNo}).subscribe(
        (res) => {
          console.log(res)
        }
      );
    }
  }



  onCheckboxOrganDonor(event: any) {
    this.patientDetails.OrganDonor = event.checked ? 'Y' : 'N';
  }



  onCheckboxSpeakEnglish(event: any) {
    this.patientDetails.SpeakEnglish = event.checked ? 'Y' : 'N';
  }

  onCheckboxAlertEmail(event: any) {
    this.patientDetails.AlertEmail = event.checked ? 'Y' : 'N';
  }
  onCheckboxAlertSMS(event: any) {
    this.patientDetails.AlertSMS = event.checked ? 'Y' : 'N';
  }

  ngOnInit(): void {
    const codeTypes = ['PT', 'SX', 'RE', 'RC', 'BG', 'NA', 'MS', 'TC', 'ED', 'ON', 'ID', 'LG', 'RT', 'AD', 'CY'];
    this.commonServices.fetchVisitData(codeTypes).subscribe(
      (response: any) => {
        this.visitData = response;
      }
    );
  }
}
